<div class="container">
  <mat-card class="list-card">
    <mat-card-header>
      <mat-card-title>Brands</mat-card-title>
      <div class="header-actions">
        <button mat-raised-button color="primary" routerLink="new" matTooltip="Add New Brand">
          <mat-icon>add</mat-icon> Add New
        </button>
        <button mat-raised-button color="warn" [disabled]="selection.isEmpty()" (click)="bulkDeleteSelected()" matTooltip="Delete Selected">
          <mat-icon>delete</mat-icon> Delete Selected
        </button>
        <button mat-icon-button (click)="refreshList()" matTooltip="Refresh List">
          <mat-icon>refresh</mat-icon>
        </button>
      </div>
    </mat-card-header>

    <mat-card-content>
      <div class="filter-container">
        <mat-form-field appearance="outline">
          <mat-label>Search</mat-label>
          <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilter()" placeholder="Search by name, subcategory, category or creator">
          <button *ngIf="searchTerm" matSuffix mat-icon-button aria-label="Clear" (click)="searchTerm=''; applyFilter()">
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>

        <!-- Add toggle for showing inactive items -->
        <mat-slide-toggle [(ngModel)]="includeInactive" (change)="toggleIncludeInactive()">
          Include Inactive
        </mat-slide-toggle>
      </div>

      <div class="loading-shade" *ngIf="isLoading">
        <mat-spinner diameter="50"></mat-spinner>
      </div>

      <div class="error-container" *ngIf="errorMessage && !isLoading">
        <p class="error-message">{{ errorMessage }}</p>
        <button mat-raised-button color="primary" (click)="loadBrands()">
          <mat-icon>refresh</mat-icon> Retry
        </button>
      </div>

      <div class="table-container" *ngIf="!isLoading && !errorMessage">
      <table mat-table [dataSource]="displayedBrands" class="brands-table" matSort>
        <!-- Checkbox Column -->
        <ng-container matColumnDef="select">
          <th mat-header-cell *matHeaderCellDef>
            <mat-checkbox (change)="$event ? masterToggle() : null"
                          [checked]="selection.hasValue() && isAllSelected()"
                          [indeterminate]="selection.hasValue() && !isAllSelected()">
            </mat-checkbox>
          </th>
          <td mat-cell *matCellDef="let row">
            <mat-checkbox (click)="$event.stopPropagation()"
                          (change)="$event ? selection.toggle(row) : null"
                          [checked]="selection.isSelected(row)">
            </mat-checkbox>
          </td>
        </ng-container>

        <!-- ID Column -->
        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>ID</th>
          <td mat-cell *matCellDef="let brand">{{ brand.id }}</td>
        </ng-container>

        <!-- Name Column -->
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Brand Name</th>
          <td mat-cell *matCellDef="let brand">
            <strong>{{ brand.name }}</strong>
          </td>
        </ng-container>

        <!-- Product Subcategory Column -->
        <ng-container matColumnDef="product_subcategory_name">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Subcategory</th>
          <td mat-cell *matCellDef="let brand">
            <mat-chip class="subcategory-chip">{{ brand.product_subcategory_name }}</mat-chip>
          </td>
        </ng-container>

        <!-- Product Category Column -->
        <ng-container matColumnDef="product_category_name">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Category</th>
          <td mat-cell *matCellDef="let brand">
            <mat-chip class="category-chip">{{ brand.product_category_name }}</mat-chip>
          </td>
        </ng-container>

        <!-- Created By Column -->
        <ng-container matColumnDef="created_by">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Created By</th>
          <td mat-cell *matCellDef="let brand">{{ brand.created_by_username }}</td>
        </ng-container>

        <!-- Status Column -->
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
          <td mat-cell *matCellDef="let brand">
            <mat-slide-toggle
              [checked]="brand.is_active"
              (change)="toggleBrandStatus(brand)"
              [matTooltip]="brand.is_active ? 'Click to deactivate' : 'Click to activate'"
              color="primary">
            </mat-slide-toggle>
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let brand">
            <button mat-icon-button [routerLink]="[brand.id]" matTooltip="View Details">
              <mat-icon>visibility</mat-icon>
            </button>
            <button mat-icon-button [routerLink]="['edit', brand.id]" matTooltip="Edit">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-icon-button (click)="toggleBrandStatus(brand)" matTooltip="{{ brand.is_active ? 'Deactivate' : 'Activate' }}">
              <mat-icon>{{ brand.is_active ? 'toggle_on' : 'toggle_off' }}</mat-icon>
            </button>
            <button mat-icon-button (click)="deleteBrand(brand)" matTooltip="Delete">
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>

      <mat-paginator
        [length]="totalBrands"
        [pageSize]="pageSize"
        [pageSizeOptions]="pageSizeOptions"
        (page)="onPageChange($event)"
        showFirstLastButtons>
      </mat-paginator>
      </div>

      <div class="no-data" *ngIf="!isLoading && !errorMessage && displayedBrands.data.length === 0">
      <mat-icon>inventory_2</mat-icon>
      <h3>No brands found</h3>
      <p>{{ searchTerm ? 'No brands match your search criteria.' : 'Start by adding your first brand.' }}</p>
      <button mat-raised-button color="primary" routerLink="new" *ngIf="!searchTerm">
        <mat-icon>add</mat-icon> Add First Brand
      </button>
    </div>
    </mat-card-content>
  </mat-card>
</div>
